# ESX Advanced Garage - Custom UI

این ریسورس حالا دارای رابط کاربری کاستوم و مدرن برای نمایش ماشین‌ها می‌باشد.

## ویژگی‌های جدید UI

### طراحی مدرن
- رابط کاربری زیبا و مدرن با طراحی کارت‌ها
- پشتیبانی کامل از زبان فارسی (RTL)
- انیمیشن‌ها و افکت‌های بصری جذاب
- طراحی ریسپانسیو برای اندازه‌های مختلف صفحه

### قابلیت‌های کاربردی
- **جستجو**: جستجو بر اساس نام ماشین یا پلاک
- **فیلتر**: فیلتر کردن ماشین‌ها بر اساس وضعیت (همه، در گاراژ، توقیف شده)
- **نمایش اطلاعات**: نمایش کامل اطلاعات ماشین شامل:
  - نام ماشین
  - پلاک
  - وضعیت (در گاراژ یا توقیف شده)
  - موقعیت
  - وضعیت آسیب (در صورت وجود)

### عملکردها
- **خروج از گاراژ**: خروج ماشین از گاراژ با یک کلیک
- **بازگرداندن از توقیف**: بازگرداندن ماشین از پارکینگ توقیف
- **تأیید عملیات**: مودال تأیید برای عملیات‌های مهم
- **نمایش هزینه**: نمایش هزینه بازگرداندن از توقیف

## تغییرات انجام شده

### فایل‌های جدید
- `html/index.html` - ساختار HTML رابط کاربری
- `html/style.css` - استایل‌های CSS
- `html/script.js` - منطق JavaScript

### تغییرات در فایل‌های موجود
- `client/main.lua` - تغییر تابع `ListOwnedCarsMenu()` برای استفاده از UI کاستوم
- `fxmanifest.lua` - اضافه کردن فایل‌های UI

## نحوه کارکرد

1. هنگام ورود به گاراژ و انتخاب "لیست ماشین‌های شخصی"
2. UI کاستوم باز می‌شود و لیست ماشین‌ها را نمایش می‌دهد
3. کاربر می‌تواند:
   - جستجو کند
   - فیلتر اعمال کند
   - روی دکمه‌های عملیات کلیک کند
4. عملیات انتخاب شده انجام می‌شود

## سازگاری

- این UI فقط برای ماشین‌های شخصی فعال شده است
- سایر منوها (قایق‌ها، هواپیماها، توقیف) هنوز از منوی قدیمی استفاده می‌کنند
- می‌توان به راحتی سایر منوها را نیز به UI کاستوم تبدیل کرد

## توسعه آینده

برای تبدیل سایر منوها به UI کاستوم:
1. توابع مربوطه در `client/main.lua` را مشابه `ListOwnedCarsMenu()` تغییر دهید
2. در `script.js` نوع گاراژ جدید را اضافه کنید
3. در صورت نیاز، استایل‌های خاص را در `style.css` اضافه کنید

## نکات فنی

- از NUI برای ارتباط بین Lua و JavaScript استفاده شده
- UI کاملاً ریسپانسیو و موبایل فرندلی است
- کدها بهینه و قابل توسعه نوشته شده‌اند
- پشتیبانی کامل از ESC برای بستن UI
