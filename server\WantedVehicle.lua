function CreateVehicleWanted(plate, source)

    local self = {}
    self.plate  = plate
    self.source = source
    self.found = nil

    self.setState = function(v)
        if not self.found then
            self.found = v
        end
    end

    -- SetTimeout(1000 * 60 * 5, function()
    SetTimeout(500 * 50 * 5, function()
        if self.found ~= false then
            exports.ghmattimysql:execute('UPDATE `owned_vehicles` SET `stored` = @stored, `parkmeter` = @parkmeter WHERE `plate` = @plate',{
                ['@stored'] = true,
                ['@parkmeter'] = false,
                ['@garagenum'] = 0,
                ['@plate']  = self.plate,
                -- ['@damage']  = '{"body_health":0.0,"damaged_windows":[0,1,2,3,4,5,6,7],"broken_doors":[0,1,2,3,4,5,6,7],"burst_tires":[],"engine_health":100.0}'
				
            }, function()
                TriggerClientEvent('esx:showNotification', self.source, '~g~~h~Mashine Shoma Peyda Shod, Be parking moraje\'e konid')                    
            end)
        else
            TriggerClientEvent('esx:showNotification', self.source, '~r~~h~Mashine Shoma Peyda nashod va poole shoma bargasht khord')
        end
        VehicleWanted[self.plate] = nil
    end)
    
    return self
end

function DeleteEVehicleByPlate(plate)
    for k,v in pairs(GetAllVehicles()) do
        if DoesEntityExist(v) then
            if plate == ESX.Math.Trim(GetVehicleNumberPlateText(v)) then
                Citizen.InvokeNative(0xFAA3D236,v)
                break
            end

        end
    end
end