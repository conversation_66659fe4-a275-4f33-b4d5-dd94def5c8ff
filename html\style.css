/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    overflow: hidden;
    direction: rtl;
}

/* Main Container */
.garage-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    display: flex;
    flex-direction: column;
}

/* Header */
.garage-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    padding: 20px 30px;
    border-bottom: 3px solid #3498db;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.header-title {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-title i {
    font-size: 2rem;
    color: #3498db;
}

.header-title h1 {
    font-size: 1.8rem;
    font-weight: 600;
    color: white;
}

.close-btn {
    background: #e74c3c;
    border: none;
    color: white;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: #c0392b;
    transform: scale(1.1);
}

/* Search Section */
.search-section {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px 30px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.search-container {
    position: relative;
    max-width: 500px;
    margin: 0 auto 20px;
}

.search-container i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #bdc3c7;
    font-size: 1.1rem;
}

#vehicle-search {
    width: 100%;
    padding: 15px 50px 15px 20px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1rem;
    font-family: 'Vazir', Arial, sans-serif;
    transition: all 0.3s ease;
}

#vehicle-search::placeholder {
    color: #bdc3c7;
}

#vehicle-search:focus {
    outline: none;
    border-color: #3498db;
    background: rgba(255, 255, 255, 0.15);
}

.filter-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 10px 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    background: transparent;
    color: white;
    font-family: 'Vazir', Arial, sans-serif;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
    background: #3498db;
    border-color: #3498db;
    transform: translateY(-2px);
}

/* Vehicles Container */
.vehicles-container {
    flex: 1;
    overflow-y: auto;
    padding: 30px;
}

.vehicles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 25px;
    max-width: 1400px;
    margin: 0 auto;
}

/* Vehicle Card */
.vehicle-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
}

.vehicle-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

.vehicle-image {
    position: relative;
    height: 200px;
    overflow: hidden;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.vehicle-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.vehicle-card:hover .vehicle-image img {
    transform: scale(1.05);
}

.vehicle-status {
    position: absolute;
    top: 15px;
    left: 15px;
}

.status-badge {
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.status-badge.stored {
    background: #27ae60;
}

.status-badge.impounded {
    background: #e74c3c;
}

/* Vehicle Info */
.vehicle-info {
    padding: 20px;
}

.vehicle-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.vehicle-name {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
}

.vehicle-plate {
    background: #34495e;
    color: white;
    padding: 5px 10px;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
}

.vehicle-details {
    margin-bottom: 20px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    color: #7f8c8d;
    font-size: 0.9rem;
}

.detail-item i {
    width: 16px;
    text-align: center;
}

/* Vehicle Actions */
.vehicle-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-family: 'Vazir', Arial, sans-serif;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.spawn-btn {
    background: #27ae60;
    color: white;
}

.spawn-btn:hover {
    background: #229954;
    transform: translateY(-2px);
}

.return-btn {
    background: #f39c12;
    color: white;
}

.return-btn:hover {
    background: #e67e22;
    transform: translateY(-2px);
}

.action-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: rgba(255, 255, 255, 0.8);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
}

/* Loading State */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: rgba(255, 255, 255, 0.8);
}

.loading-spinner i {
    font-size: 3rem;
    margin-bottom: 20px;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal-content {
    background: white;
    border-radius: 15px;
    max-width: 500px;
    width: 90%;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.modal-header {
    background: #34495e;
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 30px;
    text-align: center;
}

.modal-cost {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    color: #e74c3c;
    font-weight: 600;
}

.modal-actions {
    padding: 20px;
    display: flex;
    gap: 15px;
    justify-content: center;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 8px;
    font-family: 'Vazir', Arial, sans-serif;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-cancel {
    background: #95a5a6;
    color: white;
}

.btn-cancel:hover {
    background: #7f8c8d;
}

.btn-confirm {
    background: #3498db;
    color: white;
}

.btn-confirm:hover {
    background: #2980b9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .vehicles-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .garage-header {
        padding: 15px 20px;
    }
    
    .header-title h1 {
        font-size: 1.5rem;
    }
    
    .search-section {
        padding: 15px 20px;
    }
    
    .vehicles-container {
        padding: 20px;
    }
}
