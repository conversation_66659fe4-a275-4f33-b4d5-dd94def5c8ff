<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>گاراژ پیشرفته</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Vazir:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div id="garage-ui" class="garage-container" style="display: none;">
        <!-- Header -->
        <div class="garage-header">
            <div class="header-content">
                <div class="header-title">
                    <i class="fas fa-car"></i>
                    <h1 id="garage-title">گاراژ شخصی</h1>
                </div>
                <button class="close-btn" onclick="closeGarage()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- Search and Filter -->
        <div class="search-section">
            <div class="search-container">
                <i class="fas fa-search"></i>
                <input type="text" id="vehicle-search" placeholder="جستجو بر اساس نام ماشین یا پلاک...">
            </div>
            <div class="filter-buttons">
                <button class="filter-btn active" data-filter="all">همه</button>
                <button class="filter-btn" data-filter="stored">در گاراژ</button>
                <button class="filter-btn" data-filter="impounded">توقیف شده</button>
            </div>
        </div>

        <!-- Vehicle Grid -->
        <div class="vehicles-container">
            <div id="vehicles-grid" class="vehicles-grid">
                <!-- Vehicle cards will be populated here -->
            </div>
            
            <!-- Empty State -->
            <div id="empty-state" class="empty-state" style="display: none;">
                <i class="fas fa-car-side"></i>
                <h3>هیچ ماشینی یافت نشد</h3>
                <p>شما در حال حاضر هیچ ماشینی در این گاراژ ندارید.</p>
            </div>
        </div>

        <!-- Loading State -->
        <div id="loading-state" class="loading-state">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
            </div>
            <p>در حال بارگذاری ماشین‌ها...</p>
        </div>
    </div>

    <!-- Vehicle Card Template -->
    <template id="vehicle-card-template">
        <div class="vehicle-card" data-vehicle-id="">
            <div class="vehicle-image">
                <img src="" alt="Vehicle Image" onerror="this.src='https://via.placeholder.com/300x200?text=No+Image'">
                <div class="vehicle-status">
                    <span class="status-badge"></span>
                </div>
            </div>
            <div class="vehicle-info">
                <div class="vehicle-header">
                    <h3 class="vehicle-name"></h3>
                    <span class="vehicle-plate"></span>
                </div>
                <div class="vehicle-details">
                    <div class="detail-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span class="vehicle-location"></span>
                    </div>
                    <div class="detail-item damage-info" style="display: none;">
                        <i class="fas fa-wrench"></i>
                        <span class="vehicle-damage"></span>
                    </div>
                </div>
                <div class="vehicle-actions">
                    <button class="action-btn spawn-btn" onclick="spawnVehicle(this)">
                        <i class="fas fa-play"></i>
                        <span>خروج از گاراژ</span>
                    </button>
                    <button class="action-btn return-btn" onclick="returnVehicle(this)" style="display: none;">
                        <i class="fas fa-undo"></i>
                        <span>بازگرداندن</span>
                    </button>
                </div>
            </div>
        </div>
    </template>

    <!-- Confirmation Modal -->
    <div id="confirmation-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">تأیید عملیات</h3>
                <button class="close-btn" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p id="modal-message">آیا مطمئن هستید؟</p>
                <div class="modal-cost" id="modal-cost" style="display: none;">
                    <i class="fas fa-dollar-sign"></i>
                    <span>هزینه: $<span id="cost-amount">0</span></span>
                </div>
            </div>
            <div class="modal-actions">
                <button class="btn btn-cancel" onclick="closeModal()">انصراف</button>
                <button class="btn btn-confirm" onclick="confirmAction()">تأیید</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
