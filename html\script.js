// Global variables
let currentVehicles = [];
let currentFilter = 'all';
let currentAction = null;
let currentVehicleData = null;

// Initialize UI when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeUI();
    setupEventListeners();
});

// Initialize UI components
function initializeUI() {
    // Hide loading state initially
    document.getElementById('loading-state').style.display = 'none';
    
    // Setup search functionality
    const searchInput = document.getElementById('vehicle-search');
    searchInput.addEventListener('input', debounce(filterVehicles, 300));
    
    // Setup filter buttons
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            setActiveFilter(this.dataset.filter);
        });
    });
}

// Setup event listeners for NUI communication
function setupEventListeners() {
    window.addEventListener('message', function(event) {
        const data = event.data;
        
        switch(data.action) {
            case 'openGarage':
                openGarage(data.garageType, data.vehicles);
                break;
            case 'closeGarage':
                closeGarage();
                break;
            case 'updateVehicles':
                updateVehicleList(data.vehicles);
                break;
            case 'showNotification':
                showNotification(data.message, data.type);
                break;
        }
    });
    
    // Close garage on ESC key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeGarage();
        }
    });
}

// Open garage UI
function openGarage(garageType, vehicles) {
    const garageUI = document.getElementById('garage-ui');
    const garageTitle = document.getElementById('garage-title');
    
    // Set title based on garage type
    const titles = {
        'car': 'گاراژ ماشین‌های شخصی',
        'boat': 'گاراژ قایق‌ها',
        'aircraft': 'گاراژ هواپیماها',
        'pound_car': 'پارکینگ توقیف ماشین‌ها',
        'pound_boat': 'پارکینگ توقیف قایق‌ها',
        'pound_aircraft': 'پارکینگ توقیف هواپیماها',
        'policing': 'گاراژ پلیس',
        'ambulance': 'گاراژ آمبولانس'
    };
    
    garageTitle.textContent = titles[garageType] || 'گاراژ';
    
    // Show UI
    garageUI.style.display = 'flex';
    document.body.style.overflow = 'hidden';
    
    // Load vehicles
    updateVehicleList(vehicles);
    
    // Send ready signal to Lua
    fetch(`https://${GetParentResourceName()}/uiReady`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    });
}

// Close garage UI
function closeGarage() {
    const garageUI = document.getElementById('garage-ui');
    garageUI.style.display = 'none';
    document.body.style.overflow = 'auto';
    
    // Clear search and filters
    document.getElementById('vehicle-search').value = '';
    setActiveFilter('all');
    
    // Send close signal to Lua
    fetch(`https://${GetParentResourceName()}/closeGarage`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    });
}

// Update vehicle list
function updateVehicleList(vehicles) {
    currentVehicles = vehicles || [];
    
    const loadingState = document.getElementById('loading-state');
    const emptyState = document.getElementById('empty-state');
    const vehiclesGrid = document.getElementById('vehicles-grid');
    
    // Hide loading
    loadingState.style.display = 'none';
    
    // Clear existing vehicles
    vehiclesGrid.innerHTML = '';
    
    if (currentVehicles.length === 0) {
        emptyState.style.display = 'block';
        return;
    }
    
    emptyState.style.display = 'none';
    
    // Create vehicle cards
    currentVehicles.forEach(vehicle => {
        const card = createVehicleCard(vehicle);
        vehiclesGrid.appendChild(card);
    });
    
    // Apply current filter
    filterVehicles();
}

// Create vehicle card element
function createVehicleCard(vehicle) {
    const template = document.getElementById('vehicle-card-template');
    const card = template.content.cloneNode(true);
    
    // Set vehicle data
    const cardElement = card.querySelector('.vehicle-card');
    cardElement.dataset.vehicleId = vehicle.plate;
    cardElement.dataset.vehicleData = JSON.stringify(vehicle);
    
    // Set vehicle image (placeholder for now)
    const img = card.querySelector('.vehicle-image img');
    img.src = getVehicleImage(vehicle.vehicle.model);
    img.alt = vehicle.vehicleName || 'Vehicle';
    
    // Check if stored is true (1) or false (0)
    const isStored = vehicle.stored === true || vehicle.stored === 1;

    // Set status badge
    const statusBadge = card.querySelector('.status-badge');
    if (isStored) {
        statusBadge.textContent = 'در گاراژ';
        statusBadge.className = 'status-badge stored';
    } else {
        statusBadge.textContent = 'توقیف شده';
        statusBadge.className = 'status-badge impounded';
    }

    // Set vehicle info
    card.querySelector('.vehicle-name').textContent = vehicle.vehicleName || 'نامشخص';
    card.querySelector('.vehicle-plate').textContent = vehicle.plate;
    card.querySelector('.vehicle-location').textContent = isStored ? 'گاراژ' : 'پارکینگ توقیف';
    
    // Set damage info if available
    if (vehicle.damage && vehicle.damage !== '{}') {
        const damageInfo = card.querySelector('.damage-info');
        damageInfo.style.display = 'flex';
        card.querySelector('.vehicle-damage').textContent = 'آسیب دیده';
    }
    
    // Set action buttons
    const spawnBtn = card.querySelector('.spawn-btn');
    const returnBtn = card.querySelector('.return-btn');

    if (isStored) {
        spawnBtn.style.display = 'flex';
        returnBtn.style.display = 'none';
        spawnBtn.onclick = () => spawnVehicle(vehicle);
    } else {
        spawnBtn.style.display = 'none';
        returnBtn.style.display = 'flex';
        returnBtn.onclick = () => returnVehicle(vehicle);
    }
    
    return card;
}

// Get vehicle image (placeholder function)
function getVehicleImage(model) {
    // This could be expanded to use actual vehicle images based on model
    const vehicleName = model ? 'ماشین' : 'ماشین';
    return `https://via.placeholder.com/300x200/3498db/ffffff?text=${encodeURIComponent(vehicleName)}`;
}

// Spawn vehicle
function spawnVehicle(vehicle) {
    if (typeof vehicle === 'object') {
        currentVehicleData = vehicle;
    } else {
        // If called from button, get data from parent card
        const card = vehicle.closest('.vehicle-card');
        currentVehicleData = JSON.parse(card.dataset.vehicleData);
    }
    
    currentAction = 'spawn';
    
    showConfirmationModal(
        'خروج ماشین از گاراژ',
        `آیا می‌خواهید ماشین ${currentVehicleData.vehicleName} با پلاک ${currentVehicleData.plate} را از گاراژ خارج کنید؟`
    );
}

// Return vehicle from pound
function returnVehicle(vehicle) {
    if (typeof vehicle === 'object') {
        currentVehicleData = vehicle;
    } else {
        // If called from button, get data from parent card
        const card = vehicle.closest('.vehicle-card');
        currentVehicleData = JSON.parse(card.dataset.vehicleData);
    }

    // Check if vehicle is actually impounded
    const isStored = currentVehicleData.stored === true || currentVehicleData.stored === 1;
    if (isStored) {
        console.log('Vehicle is stored, cannot return from pound');
        return;
    }

    currentAction = 'return';

    // Show cost if available
    const cost = currentVehicleData.returnCost || 0;

    showConfirmationModal(
        'بازگرداندن ماشین',
        `آیا می‌خواهید ماشین ${currentVehicleData.vehicleName} با پلاک ${currentVehicleData.plate} را بازگردانید؟`,
        cost
    );
}

// Show confirmation modal
function showConfirmationModal(title, message, cost = 0) {
    const modal = document.getElementById('confirmation-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalMessage = document.getElementById('modal-message');
    const modalCost = document.getElementById('modal-cost');
    const costAmount = document.getElementById('cost-amount');
    
    modalTitle.textContent = title;
    modalMessage.textContent = message;
    
    if (cost > 0) {
        modalCost.style.display = 'flex';
        costAmount.textContent = cost;
    } else {
        modalCost.style.display = 'none';
    }
    
    modal.style.display = 'flex';
}

// Close modal
function closeModal() {
    const modal = document.getElementById('confirmation-modal');
    modal.style.display = 'none';
    currentAction = null;
    currentVehicleData = null;
}

// Confirm action
function confirmAction() {
    if (!currentAction || !currentVehicleData) return;
    
    const actionData = {
        action: currentAction,
        vehicle: currentVehicleData
    };
    
    fetch(`https://${GetParentResourceName()}/vehicleAction`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(actionData)
    });
    
    closeModal();
}

// Filter vehicles
function filterVehicles() {
    const searchTerm = document.getElementById('vehicle-search').value.toLowerCase();
    const cards = document.querySelectorAll('.vehicle-card');
    
    cards.forEach(card => {
        const vehicleData = JSON.parse(card.dataset.vehicleData);
        const vehicleName = (vehicleData.vehicleName || '').toLowerCase();
        const plate = (vehicleData.plate || '').toLowerCase();
        
        // Check search term
        const matchesSearch = !searchTerm || 
            vehicleName.includes(searchTerm) || 
            plate.includes(searchTerm);
        
        // Check filter
        const isStored = vehicleData.stored === true || vehicleData.stored === 1;
        const matchesFilter = currentFilter === 'all' ||
            (currentFilter === 'stored' && isStored) ||
            (currentFilter === 'impounded' && !isStored);
        
        card.style.display = (matchesSearch && matchesFilter) ? 'block' : 'none';
    });
}

// Set active filter
function setActiveFilter(filter) {
    currentFilter = filter;
    
    // Update button states
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(btn => {
        btn.classList.toggle('active', btn.dataset.filter === filter);
    });
    
    // Apply filter
    filterVehicles();
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Show notification (can be expanded)
function showNotification(message, type = 'info') {
    console.log(`[${type.toUpperCase()}] ${message}`);
    // This could be expanded to show actual notifications in the UI
}

// Get parent resource name for NUI callbacks
function GetParentResourceName() {
    return window.location.hostname === '' ? 'esx_advancedgarage' : window.location.hostname;
}
